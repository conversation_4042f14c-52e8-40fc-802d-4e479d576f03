interface LlamaIndexResponse {
  id: string;
  status?: string;
  error?: string;
}

interface ProjectResponse {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

interface FileResponse {
  id: string;
  name: string;
  project_id: string;
  status: string;
  created_at: string;
  updated_at: string;
}

interface PipelineResponse {
  id: string;
  name: string;
  project_id: string;
  status: string;
  created_at: string;
  updated_at: string;
}

interface RetrieverResponse {
  id: string;
  name: string;
  project_id: string;
  pipeline_ids: string[];
  created_at: string;
  updated_at: string;
}

interface RetrieveResponse {
  nodes: Array<{
    text: string;
    metadata: {
      page?: number;
      source?: string;
      file_name?: string;
    };
    score: number;
  }>;
}

export class LlamaIndexService {
  private apiKey: string;
  private baseUrl = 'https://api.cloud.llamaindex.ai/api/v1';
  private isConfigured: boolean;
  private defaultProjectId: string | null = null;

  constructor() {
    this.apiKey =
      process.env.LLAMA_CLOUD_API_KEY || process.env.LLAMACLOUD_API_KEY || '';
    this.isConfigured = !!this.apiKey;

    if (!this.apiKey) {
      console.warn(
        'WARNING: LLAMA_CLOUD_API_KEY not configured. Document indexing will be disabled.',
      );
      console.warn(
        'For full functionality, please set LLAMA_CLOUD_API_KEY in Railway environment variables.',
      );
    } else {
      console.log('LlamaIndexService initialized successfully');
    }
  }

  private checkConfiguration(): void {
    if (!this.isConfigured) {
      throw new Error(
        'Document indexing service is currently unavailable. Please try again later.',
      );
    }
  }

  /**
   * Sanitizes third-party API errors to prevent exposing sensitive information
   */
  private sanitizeError(error: any, context: string): Error {
    // Log the actual error for debugging
    console.error(`LlamaIndex ${context} error:`, error);

    // Return a generic user-friendly message
    if (error?.message?.includes('timeout') || error?.name === 'AbortError') {
      return new Error(
        'Document indexing is taking longer than expected. Please try again.',
      );
    }

    if (error?.message?.includes('401') || error?.message?.includes('403')) {
      return new Error(
        'Document indexing service is temporarily unavailable. Please try again later.',
      );
    }

    if (error?.message?.includes('429')) {
      return new Error(
        'Document indexing service is busy. Please try again in a few moments.',
      );
    }

    // Generic fallback message
    return new Error('Unable to index document. Please try again.');
  }

  // ==================== Project Management ====================

  async createProject(
    name: string,
    description?: string,
  ): Promise<ProjectResponse> {
    this.checkConfiguration();

    try {
      const payload = {
        name,
        description: description || `ChatAI Project: ${name}`,
      };

      const response = await fetch(`${this.baseUrl}/projects`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaIndex createProject error: ${response.status} - ${errorText}`,
        );
        throw this.sanitizeError(
          { status: response.status, message: errorText },
          'createProject',
        );
      }

      const project = await response.json();
      this.defaultProjectId = project.id;
      return project;
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes('Document indexing')
      ) {
        throw error;
      }
      throw this.sanitizeError(error, 'createProject');
    }
  }

  async getOrCreateProject(name: string): Promise<ProjectResponse> {
    this.checkConfiguration();

    try {
      // First, try to list existing projects to see if one with this name exists
      const listResponse = await fetch(`${this.baseUrl}/projects`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
      });

      if (listResponse.ok) {
        const projects = await listResponse.json();

        // Look for existing project with the same name
        const existingProject = projects.find(
          (p: ProjectResponse) => p.name === name,
        );
        if (existingProject) {
          this.defaultProjectId = existingProject.id;
          return existingProject;
        }

        // If no project with the name exists, but there are existing projects,
        // use the first one (for free tier accounts restricted to single project)
        if (projects.length > 0) {
          console.log(
            `Using existing project: ${projects[0].name} (${projects[0].id})`,
          );
          this.defaultProjectId = projects[0].id;
          return projects[0];
        }
      }

      // If no existing projects found, try to create a new one
      return await this.createProject(name);
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes('Document indexing')
      ) {
        throw error;
      }
      throw this.sanitizeError(error, 'getOrCreateProject');
    }
  }

  // ==================== File Management ====================

  async uploadFile(
    fileBuffer: Buffer,
    fileName: string,
    projectId?: string,
  ): Promise<FileResponse> {
    this.checkConfiguration();

    try {
      const targetProjectId = projectId || this.defaultProjectId;
      if (!targetProjectId) {
        throw new Error('No project ID available. Create a project first.');
      }

      // Create FormData for file upload
      const formData = new FormData();
      const blob = new Blob([fileBuffer]);
      formData.append('upload_file', blob, fileName);
      formData.append('project_id', targetProjectId);

      const response = await fetch(`${this.baseUrl}/files`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaIndex uploadFile error: ${response.status} - ${errorText}`,
        );
        throw this.sanitizeError(
          { status: response.status, message: errorText },
          'uploadFile',
        );
      }

      return await response.json();
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes('Document indexing')
      ) {
        throw error;
      }
      throw this.sanitizeError(error, 'uploadFile');
    }
  }

  async createIndex(
    parsedData: any,
    documentName: string,
  ): Promise<LlamaIndexResponse> {
    this.checkConfiguration();

    try {
      // Step 1: Get or create project
      const projectName = `ChatAI-${Date.now()}`;
      const project = await this.getOrCreateProject(projectName);
      console.log(`Using project: ${project.id}`);

      // Step 2: Create a text file from parsed data
      const textContent = parsedData.text || '';
      const fileBuffer = Buffer.from(textContent, 'utf-8');
      const fileName = `${documentName.replace(/\.[^/.]+$/, '')}.txt`;

      const file = await this.uploadFile(fileBuffer, fileName, project.id);
      console.log(`Uploaded file: ${file.id}`);

      // Step 3: For free tier accounts, try to use existing pipelines/retrievers
      console.log('Checking for existing pipelines and retrievers...');

      // Try to list existing retrievers first (they're what we actually need)
      try {
        const retrieversResponse = await fetch(`${this.baseUrl}/retrievers`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
          },
        });

        if (retrieversResponse.ok) {
          const retrievers = await retrieversResponse.json();
          if (retrievers.length > 0) {
            console.log(
              `Using existing retriever: ${retrievers[0].name} (${retrievers[0].id})`,
            );

            // Return the existing retriever ID as the "index" ID for compatibility
            return {
              id: retrievers[0].id,
              status: 'SUCCESS',
            };
          }
        }
      } catch (error) {
        console.log(
          'Failed to list retrievers, trying to create new pipeline...',
        );
      }

      // If no existing retrievers, try to create pipeline and retriever
      const uniqueId = Date.now() + Math.random().toString(36).substr(2, 9);
      const pipeline = await this.getOrCreatePipeline(
        `pipeline-${uniqueId}`,
        project.id,
        [file.id],
      );
      console.log(`Created pipeline: ${pipeline.id}`);

      // Step 4: Create a retriever for querying
      const retriever = await this.createRetriever(
        `retriever-${uniqueId}`,
        project.id,
        [pipeline.id],
      );
      console.log(`Created retriever: ${retriever.id}`);

      // Return the retriever ID as the "index" ID for compatibility
      return {
        id: retriever.id,
        status: 'SUCCESS',
      };
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes('Document indexing')
      ) {
        throw error;
      }
      throw this.sanitizeError(error, 'createIndex');
    }
  }

  // ==================== Pipeline Management ====================

  async getOrCreatePipeline(
    name: string,
    projectId: string,
    fileIds: string[],
  ): Promise<PipelineResponse> {
    this.checkConfiguration();

    try {
      // For free tier accounts, let's try to use a very unique name to avoid conflicts
      const uniqueName = `${name}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      const payload = {
        name: uniqueName,
        project_id: projectId,
        file_ids: fileIds,
        // Use default pipeline configuration
        config: {
          chunk_size: 1024,
          chunk_overlap: 20,
        },
      };

      const response = await fetch(`${this.baseUrl}/pipelines`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaIndex createPipeline error: ${response.status} - ${errorText}`,
        );

        // If we get a 409 conflict, it might be because there's already a pipeline
        // Let's try to list existing pipelines and use one
        if (response.status === 409) {
          console.log(
            'Pipeline conflict detected, trying to list existing pipelines...',
          );
          try {
            const listResponse = await fetch(`${this.baseUrl}/pipelines`, {
              method: 'GET',
              headers: {
                Authorization: `Bearer ${this.apiKey}`,
              },
            });

            if (listResponse.ok) {
              const pipelines = await listResponse.json();
              if (pipelines.length > 0) {
                console.log(
                  `Using existing pipeline: ${pipelines[0].name} (${pipelines[0].id})`,
                );
                return pipelines[0];
              }
            }
          } catch (listError) {
            console.log(
              'Failed to list pipelines, continuing with original error',
            );
          }
        }

        throw this.sanitizeError(
          { status: response.status, message: errorText },
          'createPipeline',
        );
      }

      return await response.json();
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes('Document indexing')
      ) {
        throw error;
      }
      throw this.sanitizeError(error, 'createPipeline');
    }
  }

  async createRetriever(
    name: string,
    projectId: string,
    pipelineIds: string[],
  ): Promise<RetrieverResponse> {
    this.checkConfiguration();

    try {
      const payload = {
        name,
        project_id: projectId,
        pipeline_ids: pipelineIds,
        // Use default retriever configuration
        config: {
          top_k: 5,
        },
      };

      const response = await fetch(`${this.baseUrl}/retrievers`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaIndex createRetriever error: ${response.status} - ${errorText}`,
        );
        throw this.sanitizeError(
          { status: response.status, message: errorText },
          'createRetriever',
        );
      }

      return await response.json();
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes('Document indexing')
      ) {
        throw error;
      }
      throw this.sanitizeError(error, 'createRetriever');
    }
  }

  async retrieve(
    retrieverId: string,
    query: string,
  ): Promise<RetrieveResponse> {
    this.checkConfiguration();

    try {
      const payload = {
        query,
        top_k: 5,
      };

      const response = await fetch(
        `${this.baseUrl}/retrievers/${retrieverId}/retrieve`,
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaIndex retrieve error: ${response.status} - ${errorText}`,
        );
        throw this.sanitizeError(
          { status: response.status, message: errorText },
          'retrieve',
        );
      }

      return await response.json();
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes('Document indexing')
      ) {
        throw error;
      }
      throw this.sanitizeError(error, 'retrieve');
    }
  }

  async deleteFile(fileId: string): Promise<LlamaIndexResponse> {
    this.checkConfiguration();

    try {
      const response = await fetch(`${this.baseUrl}/files/${fileId}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaIndex deleteFile error: ${response.status} - ${errorText}`,
        );
        throw this.sanitizeError(
          { status: response.status, message: errorText },
          'deleteFile',
        );
      }

      return {
        id: fileId,
        status: 'DELETED',
      };
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes('Document indexing')
      ) {
        throw error;
      }
      throw this.sanitizeError(error, 'deleteFile');
    }
  }

  async deleteIndex(retrieverId: string): Promise<LlamaIndexResponse> {
    this.checkConfiguration();

    try {
      // In the new API, we delete the retriever instead of an "index"
      const response = await fetch(
        `${this.baseUrl}/retrievers/${retrieverId}`,
        {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
          },
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaIndex deleteRetriever error: ${response.status} - ${errorText}`,
        );
        throw this.sanitizeError(
          { status: response.status, message: errorText },
          'deleteRetriever',
        );
      }

      return {
        id: retrieverId,
        status: 'DELETED',
      };
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes('Document indexing')
      ) {
        throw error;
      }
      throw this.sanitizeError(error, 'deleteRetriever');
    }
  }
}

export const llamaIndexService = new LlamaIndexService();
